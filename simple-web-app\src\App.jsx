import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [tasks, setTasks] = useState([
    {
      id: 1,
      text: 'Learn React',
      completed: false,
      priority: 'high',
      category: 'Learning',
      dueDate: '2024-12-31',
      createdAt: new Date().toISOString(),
      tags: ['react', 'frontend']
    },
    {
      id: 2,
      text: 'Build an awesome app',
      completed: false,
      priority: 'medium',
      category: 'Development',
      dueDate: '2024-12-25',
      createdAt: new Date().toISOString(),
      tags: ['project', 'coding']
    },
    {
      id: 3,
      text: 'Deploy to production',
      completed: false,
      priority: 'low',
      category: 'DevOps',
      dueDate: '2025-01-15',
      createdAt: new Date().toISOString(),
      tags: ['deployment', 'production']
    }
  ])
  const [newTask, setNewTask] = useState('')
  const [newTaskPriority, setNewTaskPriority] = useState('medium')
  const [newTaskCategory, setNewTaskCategory] = useState('General')
  const [newTaskDueDate, setNewTaskDueDate] = useState('')
  const [newTaskTags, setNewTaskTags] = useState('')
  const [filter, setFilter] = useState('all') // all, active, completed
  const [sortBy, setSortBy] = useState('createdAt') // createdAt, dueDate, priority, alphabetical
  const [searchTerm, setSearchTerm] = useState('')
  const [showAdvancedForm, setShowAdvancedForm] = useState(false)
  const [darkMode, setDarkMode] = useState(false)
  const [categories] = useState(['General', 'Work', 'Personal', 'Learning', 'Development', 'DevOps', 'Health', 'Finance'])
  const [showStats, setShowStats] = useState(false)

  // Load tasks from localStorage on component mount
  useEffect(() => {
    const savedTasks = localStorage.getItem('premium-tasks')
    const savedDarkMode = localStorage.getItem('dark-mode')
    if (savedTasks) {
      setTasks(JSON.parse(savedTasks))
    }
    if (savedDarkMode) {
      setDarkMode(JSON.parse(savedDarkMode))
    }
  }, [])

  // Save tasks to localStorage whenever tasks change
  useEffect(() => {
    localStorage.setItem('premium-tasks', JSON.stringify(tasks))
  }, [tasks])

  // Save dark mode preference
  useEffect(() => {
    localStorage.setItem('dark-mode', JSON.stringify(darkMode))
    document.body.className = darkMode ? 'dark-mode' : ''
  }, [darkMode])

  const addTask = () => {
    if (newTask.trim()) {
      const task = {
        id: Date.now(),
        text: newTask.trim(),
        completed: false,
        priority: newTaskPriority,
        category: newTaskCategory,
        dueDate: newTaskDueDate,
        createdAt: new Date().toISOString(),
        tags: newTaskTags.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
      setTasks([...tasks, task])
      setNewTask('')
      setNewTaskPriority('medium')
      setNewTaskCategory('General')
      setNewTaskDueDate('')
      setNewTaskTags('')
      setShowAdvancedForm(false)
    }
  }

  const toggleTask = (id) => {
    setTasks(tasks.map(task =>
      task.id === id ? {
        ...task,
        completed: !task.completed,
        completedAt: !task.completed ? new Date().toISOString() : null
      } : task
    ))
  }

  const deleteTask = (id) => {
    setTasks(tasks.filter(task => task.id !== id))
  }

  const editTask = (id, newText) => {
    setTasks(tasks.map(task =>
      task.id === id ? { ...task, text: newText } : task
    ))
  }

  const duplicateTask = (id) => {
    const taskToDuplicate = tasks.find(task => task.id === id)
    if (taskToDuplicate) {
      const duplicatedTask = {
        ...taskToDuplicate,
        id: Date.now(),
        text: `${taskToDuplicate.text} (Copy)`,
        completed: false,
        createdAt: new Date().toISOString(),
        completedAt: null
      }
      setTasks([...tasks, duplicatedTask])
    }
  }

  const getPriorityWeight = (priority) => {
    switch (priority) {
      case 'high': return 3
      case 'medium': return 2
      case 'low': return 1
      default: return 0
    }
  }

  const isOverdue = (dueDate) => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date()
  }

  const getDaysUntilDue = (dueDate) => {
    if (!dueDate) return null
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const filteredAndSortedTasks = tasks
    .filter(task => {
      // Filter by completion status
      if (filter === 'active') return !task.completed
      if (filter === 'completed') return task.completed
      if (filter === 'overdue') return !task.completed && isOverdue(task.dueDate)
      return true
    })
    .filter(task => {
      // Filter by search term
      if (!searchTerm) return true
      const searchLower = searchTerm.toLowerCase()
      return (
        task.text.toLowerCase().includes(searchLower) ||
        task.category.toLowerCase().includes(searchLower) ||
        task.tags.some(tag => tag.toLowerCase().includes(searchLower))
      )
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0
          if (!a.dueDate) return 1
          if (!b.dueDate) return -1
          return new Date(a.dueDate) - new Date(b.dueDate)
        case 'priority':
          return getPriorityWeight(b.priority) - getPriorityWeight(a.priority)
        case 'alphabetical':
          return a.text.localeCompare(b.text)
        case 'category':
          return a.category.localeCompare(b.category)
        default: // createdAt
          return new Date(b.createdAt) - new Date(a.createdAt)
      }
    })

  const completedCount = tasks.filter(task => task.completed).length
  const totalCount = tasks.length
  const overdueCount = tasks.filter(task => !task.completed && isOverdue(task.dueDate)).length
  const todayCount = tasks.filter(task => {
    if (!task.dueDate) return false
    const today = new Date().toDateString()
    const taskDate = new Date(task.dueDate).toDateString()
    return today === taskDate && !task.completed
  }).length

  const exportTasks = () => {
    const dataStr = JSON.stringify(tasks, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'tasks-export.json'
    link.click()
  }

  const clearCompleted = () => {
    setTasks(tasks.filter(task => !task.completed))
  }

  return (
    <div className={`app ${darkMode ? 'dark-mode' : ''}`}>
      <header className="app-header">
        <div className="header-top">
          <h1>🚀 Premium Task Manager</h1>
          <div className="header-controls">
            <button
              onClick={() => setDarkMode(!darkMode)}
              className="theme-toggle"
              title="Toggle Dark Mode"
            >
              {darkMode ? '☀️' : '🌙'}
            </button>
            <button
              onClick={() => setShowStats(!showStats)}
              className="stats-toggle"
              title="Toggle Statistics"
            >
              📊
            </button>
            <button
              onClick={exportTasks}
              className="export-button"
              title="Export Tasks"
            >
              📥
            </button>
          </div>
        </div>
        <p>Advanced task management with premium features!</p>
      </header>

      {showStats && (
        <div className="stats-panel">
          <div className="stat-card">
            <div className="stat-number">{totalCount}</div>
            <div className="stat-label">Total Tasks</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{completedCount}</div>
            <div className="stat-label">Completed</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{overdueCount}</div>
            <div className="stat-label">Overdue</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{todayCount}</div>
            <div className="stat-label">Due Today</div>
          </div>
        </div>
      )}

      <div className="controls-section">
        <div className="search-bar">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="🔍 Search tasks, categories, or tags..."
            className="search-input"
          />
        </div>

        <div className="sort-controls">
          <label>Sort by:</label>
          <select value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="sort-select">
            <option value="createdAt">Created Date</option>
            <option value="dueDate">Due Date</option>
            <option value="priority">Priority</option>
            <option value="alphabetical">Alphabetical</option>
            <option value="category">Category</option>
          </select>
        </div>
      </div>

      <div className="task-input-section">
        <div className="task-input">
          <input
            type="text"
            value={newTask}
            onChange={(e) => setNewTask(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addTask()}
            placeholder="Add a new task..."
            className="task-input-field"
          />
          <button
            onClick={() => setShowAdvancedForm(!showAdvancedForm)}
            className="advanced-toggle"
            title="Advanced Options"
          >
            ⚙️
          </button>
          <button onClick={addTask} className="add-button">
            Add Task
          </button>
        </div>

        {showAdvancedForm && (
          <div className="advanced-form">
            <div className="form-row">
              <select
                value={newTaskPriority}
                onChange={(e) => setNewTaskPriority(e.target.value)}
                className="priority-select"
              >
                <option value="low">🟢 Low Priority</option>
                <option value="medium">🟡 Medium Priority</option>
                <option value="high">🔴 High Priority</option>
              </select>

              <select
                value={newTaskCategory}
                onChange={(e) => setNewTaskCategory(e.target.value)}
                className="category-select"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>

            <div className="form-row">
              <input
                type="date"
                value={newTaskDueDate}
                onChange={(e) => setNewTaskDueDate(e.target.value)}
                className="date-input"
                title="Due Date"
              />

              <input
                type="text"
                value={newTaskTags}
                onChange={(e) => setNewTaskTags(e.target.value)}
                placeholder="Tags (comma separated)"
                className="tags-input"
              />
            </div>
          </div>
        )}
      </div>

      <div className="filter-buttons">
        <button
          className={filter === 'all' ? 'active' : ''}
          onClick={() => setFilter('all')}
        >
          All ({totalCount})
        </button>
        <button
          className={filter === 'active' ? 'active' : ''}
          onClick={() => setFilter('active')}
        >
          Active ({totalCount - completedCount})
        </button>
        <button
          className={filter === 'completed' ? 'active' : ''}
          onClick={() => setFilter('completed')}
        >
          Completed ({completedCount})
        </button>
        <button
          className={filter === 'overdue' ? 'active' : ''}
          onClick={() => setFilter('overdue')}
        >
          Overdue ({overdueCount})
        </button>
      </div>

      <div className="bulk-actions">
        <button onClick={clearCompleted} className="bulk-action-btn" disabled={completedCount === 0}>
          Clear Completed ({completedCount})
        </button>
      </div>

      <div className="task-list">
        {filteredAndSortedTasks.length === 0 ? (
          <div className="empty-state">
            <p>
              {filter === 'all' ? 'No tasks yet. Add one above!' :
               filter === 'active' ? 'No active tasks. Great job!' :
               filter === 'completed' ? 'No completed tasks yet. Keep going!' :
               filter === 'overdue' ? 'No overdue tasks. You\'re on track!' :
               'No tasks match your search.'}
            </p>
          </div>
        ) : (
          filteredAndSortedTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onToggle={toggleTask}
              onDelete={deleteTask}
              onEdit={editTask}
              onDuplicate={duplicateTask}
              isOverdue={isOverdue(task.dueDate)}
              daysUntilDue={getDaysUntilDue(task.dueDate)}
            />
          ))
        )}
      </div>

      <div className="progress-section">
        <div className="progress-bar">
          <div
            className="progress-fill"
            style={{ width: `${totalCount > 0 ? (completedCount / totalCount) * 100 : 0}%` }}
          ></div>
        </div>
        <p className="progress-text">
          {completedCount} of {totalCount} tasks completed
          {totalCount > 0 && ` (${Math.round((completedCount / totalCount) * 100)}%)`}
        </p>
      </div>
    </div>
  )
}

// TaskItem Component
function TaskItem({ task, onToggle, onDelete, onEdit, onDuplicate, isOverdue, daysUntilDue }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState(task.text)

  const handleEdit = () => {
    if (editText.trim() && editText !== task.text) {
      onEdit(task.id, editText.trim())
    }
    setIsEditing(false)
  }

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'high': return '🔴'
      case 'medium': return '🟡'
      case 'low': return '🟢'
      default: return '⚪'
    }
  }

  const formatDueDate = (dueDate) => {
    if (!dueDate) return null
    const date = new Date(dueDate)
    return date.toLocaleDateString()
  }

  const getDueDateStatus = () => {
    if (!task.dueDate) return null
    if (isOverdue) return 'overdue'
    if (daysUntilDue === 0) return 'today'
    if (daysUntilDue === 1) return 'tomorrow'
    if (daysUntilDue <= 3) return 'soon'
    return 'future'
  }

  return (
    <div className={`task-item ${task.completed ? 'completed' : ''} ${isOverdue ? 'overdue' : ''}`}>
      <div className="task-main">
        <input
          type="checkbox"
          checked={task.completed}
          onChange={() => onToggle(task.id)}
          className="task-checkbox"
        />

        <div className="task-content">
          {isEditing ? (
            <input
              type="text"
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
              onBlur={handleEdit}
              onKeyPress={(e) => e.key === 'Enter' && handleEdit()}
              className="task-edit-input"
              autoFocus
            />
          ) : (
            <div className="task-info">
              <span className="task-text">{task.text}</span>
              <div className="task-meta">
                <span className="task-priority">{getPriorityIcon(task.priority)}</span>
                <span className="task-category">{task.category}</span>
                {task.dueDate && (
                  <span className={`task-due-date ${getDueDateStatus()}`}>
                    📅 {formatDueDate(task.dueDate)}
                    {daysUntilDue !== null && (
                      <span className="days-until">
                        {daysUntilDue === 0 ? ' (Today)' :
                         daysUntilDue === 1 ? ' (Tomorrow)' :
                         daysUntilDue < 0 ? ` (${Math.abs(daysUntilDue)} days overdue)` :
                         ` (${daysUntilDue} days left)`}
                      </span>
                    )}
                  </span>
                )}
                {task.tags && task.tags.length > 0 && (
                  <div className="task-tags">
                    {task.tags.map((tag, index) => (
                      <span key={index} className="task-tag">#{tag}</span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="task-actions">
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="action-button edit-button"
          title="Edit Task"
        >
          ✏️
        </button>
        <button
          onClick={() => onDuplicate(task.id)}
          className="action-button duplicate-button"
          title="Duplicate Task"
        >
          📋
        </button>
        <button
          onClick={() => onDelete(task.id)}
          className="action-button delete-button"
          title="Delete Task"
        >
          ❌
        </button>
      </div>
    </div>
  )
}

export default App
