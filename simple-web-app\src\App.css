#root {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

body.dark-mode {
  background: #1a1a1a;
  color: #ffffff;
}

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.app.dark-mode {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  color: white;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-controls {
  display: flex;
  gap: 0.5rem;
}

.theme-toggle, .stats-toggle, .export-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 10px;
  padding: 0.5rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.theme-toggle:hover, .stats-toggle:hover, .export-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.app-header h1 {
  font-size: 3rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.2rem;
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
}

.stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.controls-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-bar {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
}

.sort-select {
  padding: 0.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  outline: none;
  cursor: pointer;
}

.task-input-section {
  margin-bottom: 2rem;
}

.task-input {
  display: flex;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  align-items: center;
}

.task-input-field {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.task-input-field:focus {
  border-color: #667eea;
}

.advanced-toggle {
  padding: 0.75rem;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.advanced-toggle:hover {
  background: #e9ecef;
  border-color: #667eea;
}

.add-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.advanced-form {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
  margin-top: 1rem;
  border: 2px solid #e1e5e9;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.priority-select, .category-select, .date-input, .tags-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.priority-select:focus, .category-select:focus, .date-input:focus, .tags-input:focus {
  border-color: #667eea;
}

.bulk-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.bulk-action-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.bulk-action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.bulk-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.filter-buttons button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-buttons button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.filter-buttons button.active {
  background: white;
  color: #667eea;
  border-color: white;
  font-weight: 600;
}

.task-list {
  background: white;
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  min-height: 200px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  border-radius: 15px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  border-left: 4px solid transparent;
}

.task-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
  opacity: 0.7;
  background: #d4edda;
  border-left-color: #28a745;
}

.task-item.overdue {
  border-left-color: #dc3545;
  background: #f8d7da;
}

.task-main {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.task-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin-top: 0.2rem;
}

.task-content {
  flex: 1;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-text {
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.task-item.completed .task-text {
  text-decoration: line-through;
  color: #6c757d;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
  font-size: 0.85rem;
}

.task-priority {
  font-size: 1rem;
}

.task-category {
  background: #667eea;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.task-due-date {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background: #e9ecef;
  color: #495057;
}

.task-due-date.today {
  background: #fff3cd;
  color: #856404;
}

.task-due-date.tomorrow {
  background: #d1ecf1;
  color: #0c5460;
}

.task-due-date.soon {
  background: #f8d7da;
  color: #721c24;
}

.task-due-date.overdue {
  background: #dc3545;
  color: white;
}

.days-until {
  font-weight: 600;
}

.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.task-tag {
  background: #f8f9fa;
  color: #667eea;
  padding: 0.1rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
  border: 1px solid #667eea;
}

.task-edit-input {
  width: 100%;
  padding: 0.5rem;
  border: 2px solid #667eea;
  border-radius: 8px;
  font-size: 1.1rem;
  outline: none;
  background: white;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  padding: 0.4rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.action-button:hover {
  opacity: 1;
  transform: scale(1.1);
  background: rgba(0, 0, 0, 0.05);
}

.edit-button:hover {
  background: rgba(40, 167, 69, 0.1);
}

.duplicate-button:hover {
  background: rgba(0, 123, 255, 0.1);
}

.delete-button:hover {
  background: rgba(220, 53, 69, 0.1);
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

.progress-section {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: #e9ecef;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.5s ease;
  border-radius: 5px;
}

.progress-text {
  text-align: center;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
}

@media (max-width: 768px) {
  #root {
    padding: 1rem;
  }

  .app {
    padding: 1rem;
  }

  .header-top {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .controls-section {
    flex-direction: column;
    gap: 1rem;
  }

  .search-bar {
    min-width: auto;
  }

  .task-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }

  .stats-panel {
    grid-template-columns: repeat(2, 1fr);
  }

  .task-item {
    padding: 1rem;
  }

  .task-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .task-actions {
    flex-direction: column;
    gap: 0.3rem;
  }
}

@media (max-width: 480px) {
  .stats-panel {
    grid-template-columns: 1fr;
  }

  .filter-buttons button {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .task-text {
    font-size: 1rem;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }
}

/* Dark mode specific styles */
.app.dark-mode .task-input,
.app.dark-mode .task-list,
.app.dark-mode .progress-section,
.app.dark-mode .advanced-form {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.app.dark-mode .task-item {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.app.dark-mode .task-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.app.dark-mode .task-text {
  color: white;
}

.app.dark-mode .task-input-field,
.app.dark-mode .search-input,
.app.dark-mode .sort-select,
.app.dark-mode .priority-select,
.app.dark-mode .category-select,
.app.dark-mode .date-input,
.app.dark-mode .tags-input,
.app.dark-mode .task-edit-input {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.app.dark-mode .stat-card {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.app.dark-mode .stat-number {
  color: #a78bfa;
}

.app.dark-mode .empty-state p {
  color: rgba(255, 255, 255, 0.7);
}

.app.dark-mode .progress-text {
  color: rgba(255, 255, 255, 0.8);
}

/* Animation for new tasks */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: slideInFromTop 0.3s ease-out;
}

/* Pulse animation for overdue tasks */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.task-item.overdue {
  animation: pulse 2s infinite;
}
